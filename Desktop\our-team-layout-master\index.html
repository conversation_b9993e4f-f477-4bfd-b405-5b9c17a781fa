<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <link
      rel="icon"
      href="resources/favicon.ico"
      type="image/x-icon"
      sizes="96x96"
    />

    <title>Meet the Team Section | devChallenges.io</title>

    <style>
      @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Inter', sans-serif;
        background-color: #f8fafc;
        min-height: 100vh;
        color: #333333;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 80px 40px;
      }

      .content-wrapper {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 80px;
        align-items: center;
      }

      .text-content {
        padding-right: 40px;
      }

      .team-subtitle {
        color: #6B7280;
        font-size: 14px;
        font-weight: 500;
        letter-spacing: 0.5px;
        margin-bottom: 16px;
        text-transform: uppercase;
      }

      .team-title {
        color: #111827;
        font-size: 48px;
        font-weight: 700;
        line-height: 1.2;
        margin-bottom: 24px;
      }

      .team-description {
        color: #6B7280;
        font-size: 18px;
        line-height: 1.6;
        margin-bottom: 32px;
      }

      .see-all-link {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        color: #111827;
        text-decoration: none;
        font-size: 16px;
        font-weight: 500;
        transition: all 0.3s ease;
      }

      .see-all-link:hover {
        color: #6366F1;
      }

      .see-all-link svg {
        width: 16px;
        height: 16px;
        transition: transform 0.3s ease;
      }

      .see-all-link:hover svg {
        transform: translate(2px, -2px);
      }

      .team-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-template-rows: 1fr 1fr;
        gap: 16px;
        height: 400px;
      }

      .team-member {
        border-radius: 16px;
        padding: 24px;
        position: relative;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        transition: all 0.3s ease;
      }

      .team-member:hover {
        transform: translateY(-4px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
      }

      .team-member img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 16px;
      }

      .member-info {
        position: relative;
        z-index: 2;
        color: white;
      }

      .member-name {
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 4px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      }

      .member-role {
        font-size: 14px;
        font-weight: 400;
        opacity: 0.9;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      }

      /* Add overlay gradient for better text readability */
      .team-member::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.6) 100%);
        border-radius: 16px;
        z-index: 1;
      }

      .author-info {
        font-size: 14px;
        text-align: center;
        margin-top: 80px;
        color: #6B7280;
      }

      .author-info a {
        color: #111827;
        text-decoration: none;
      }

      .author-info a:hover {
        text-decoration: underline;
      }

      /* Tablet Styles - 1024px */
      @media (max-width: 1024px) {
        .container {
          padding: 60px 32px;
        }

        .content-wrapper {
          gap: 60px;
        }

        .text-content {
          padding-right: 20px;
        }

        .team-title {
          font-size: 40px;
        }

        .team-description {
          font-size: 16px;
        }

        .team-grid {
          height: 350px;
          gap: 12px;
        }

        .member-name {
          font-size: 18px;
        }

        .member-role {
          font-size: 13px;
        }
      }

      /* Mobile Styles - 768px and below */
      @media (max-width: 768px) {
        .container {
          padding: 48px 24px;
        }

        .content-wrapper {
          grid-template-columns: 1fr;
          gap: 48px;
        }

        .text-content {
          padding-right: 0;
          text-align: center;
        }

        .team-title {
          font-size: 32px;
        }

        .team-description {
          font-size: 16px;
          margin-bottom: 24px;
        }

        .team-grid {
          grid-template-columns: 1fr;
          grid-template-rows: repeat(5, 200px);
          height: auto;
          gap: 16px;
        }

        .member-name {
          font-size: 18px;
        }

        .member-role {
          font-size: 14px;
        }

        .author-info {
          margin-top: 48px;
        }
      }

      /* Small Mobile - 412px */
      @media (max-width: 412px) {
        .container {
          padding: 32px 16px;
        }

        .team-title {
          font-size: 28px;
        }

        .team-description {
          font-size: 14px;
        }

        .team-grid {
          grid-template-rows: repeat(5, 180px);
          gap: 12px;
        }

        .team-member {
          padding: 20px;
        }

        .member-name {
          font-size: 16px;
        }

        .member-role {
          font-size: 12px;
        }
      }
    </style>
  </head>
  <body>
    <main class="container">
      <div class="content-wrapper">
        <div class="text-content">
          <h2 class="team-subtitle">Our team</h2>
          <h1 class="team-title">Meet the brain</h1>
          <p class="team-description">
            We are proud to have them as part of our community and look forward to continuing to push the boundaries of what's possible in the world of digital art.
          </p>
          <a href="#" class="see-all-link">
            <span>See all members</span>
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M7 17L17 7M17 7H7M17 7V17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </a>
        </div>

        <div class="team-grid">
          <div class="team-member" style="background: linear-gradient(135deg, #8B5FBF 0%, #6366F1 100%);">
            <img src="resources/person_1.png" alt="Liam Novak" srcset="resources/<EMAIL> 2x">
            <div class="member-info">
              <h3 class="member-name">Liam Novak</h3>
              <p class="member-role">Software Engineer</p>
            </div>
          </div>

          <div class="team-member" style="background: linear-gradient(135deg, #EC4899 0%, #F97316 100%);">
            <img src="resources/person_2.png" alt="Sophia Moretti" srcset="resources/<EMAIL> 2x">
            <div class="member-info">
              <h3 class="member-name">Sophia Moretti</h3>
              <p class="member-role">Business Development Manager</p>
            </div>
          </div>

          <div class="team-member" style="background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%);">
            <img src="resources/person_3.png" alt="Ethan Rossi" srcset="resources/<EMAIL> 2x">
            <div class="member-info">
              <h3 class="member-name">Ethan Rossi</h3>
              <p class="member-role">Business Development Manager</p>
            </div>
          </div>

          <div class="team-member" style="background: linear-gradient(135deg, #F59E0B 0%, #EAB308 100%);">
            <img src="resources/person_4.png" alt="Isabella Ricci" srcset="resources/<EMAIL> 2x">
            <div class="member-info">
              <h3 class="member-name">Isabella Ricci</h3>
              <p class="member-role">UX Designer</p>
            </div>
          </div>

          <div class="team-member" style="background: linear-gradient(135deg, #10B981 0%, #059669 100%);">
            <img src="resources/person_5.png" alt="Noah Conti" srcset="resources/<EMAIL> 2x">
            <div class="member-info">
              <h3 class="member-name">Noah Conti</h3>
              <p class="member-role">Content Creator</p>
            </div>
          </div>
        </div>
      </div>
    </main>

    <div class="author-info">
      Coded by <a href="#">Your Name Here</a> | Challenge by
      <a href="https://www.devchallenges.io?ref=challenge" target="_blank"
        >devChallenges.io</a
      >.
    </div>
  </body>
</html>
