<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <link
      rel="icon"
      href="resources/favicon.ico"
      type="image/x-icon"
      sizes="96x96"
    />

    <title>Meet the Team Section | devChallenges.io</title>

    <style>
      @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Inter', sans-serif;
        background-color: #ffffff;
        min-height: 100vh;
        color: #333333;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 80px 24px;
      }

      .team-header {
        margin-bottom: 64px;
      }

      .team-subtitle {
        color: #6B7280;
        font-size: 14px;
        font-weight: 500;
        letter-spacing: 0.5px;
        margin-bottom: 16px;
        text-transform: uppercase;
      }

      .team-title {
        color: #111827;
        font-size: 48px;
        font-weight: 700;
        line-height: 1.2;
        margin-bottom: 24px;
      }

      .team-description {
        color: #6B7280;
        font-size: 18px;
        line-height: 1.6;
        max-width: 540px;
      }

      .team-grid {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        grid-template-rows: auto auto;
        gap: 32px;
        height: auto;
      }

      /* First row - two large cards */
      .team-member:nth-child(1) {
        grid-column: 1;
        grid-row: 1;
      }

      .team-member:nth-child(2) {
        grid-column: 2;
        grid-row: 1;
      }

      /* See all members in top right */
      .see-all-members {
        grid-column: 3;
        grid-row: 1;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      /* Second row - three smaller cards */
      .team-member:nth-child(4) {
        grid-column: 1;
        grid-row: 2;
      }

      .team-member:nth-child(5) {
        grid-column: 2;
        grid-row: 2;
      }

      .team-member:nth-child(6) {
        grid-column: 3;
        grid-row: 2;
      }

      .team-member {
        background: #ffffff;
        border-radius: 12px;
        padding: 32px 24px;
        text-align: center;
        transition: all 0.3s ease;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        border: 1px solid #E5E7EB;
      }

      .team-member:hover {
        transform: translateY(-4px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
      }

      .member-image {
        margin-bottom: 24px;
      }

      .member-image img {
        width: 120px;
        height: 120px;
        border-radius: 12px;
        object-fit: cover;
      }

      .member-name {
        color: #111827;
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 8px;
      }

      .member-role {
        color: #6B7280;
        font-size: 14px;
        font-weight: 400;
      }

      .see-all-link {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #111827;
        text-decoration: none;
        font-size: 16px;
        font-weight: 500;
        transition: all 0.3s ease;
      }

      .see-all-link:hover {
        color: #6366F1;
      }

      .see-all-link svg {
        width: 16px;
        height: 16px;
        transition: transform 0.3s ease;
      }

      .see-all-link:hover svg {
        transform: translate(2px, -2px);
      }

      .author-info {
        font-size: 14px;
        text-align: center;
        margin-top: 80px;
        color: #6B7280;
      }

      .author-info a {
        color: #111827;
        text-decoration: none;
      }

      .author-info a:hover {
        text-decoration: underline;
      }

      /* Tablet Styles - 1024px */
      @media (max-width: 1024px) {
        .container {
          padding: 64px 32px;
        }

        .team-title {
          font-size: 40px;
        }

        .team-description {
          font-size: 16px;
        }

        .team-grid {
          grid-template-columns: 1fr 1fr;
          grid-template-rows: auto auto auto;
          gap: 24px;
        }

        /* First row - two large cards */
        .team-member:nth-child(1) {
          grid-column: 1;
          grid-row: 1;
        }

        .team-member:nth-child(2) {
          grid-column: 2;
          grid-row: 1;
        }

        /* Second row - see all members spans both columns */
        .see-all-members {
          grid-column: 1 / 3;
          grid-row: 2;
          justify-content: center;
          padding: 24px;
        }

        /* Third row - remaining members */
        .team-member:nth-child(4) {
          grid-column: 1;
          grid-row: 3;
        }

        .team-member:nth-child(5) {
          grid-column: 2;
          grid-row: 3;
        }

        .team-member:nth-child(6) {
          grid-column: 1 / 3;
          grid-row: 4;
          max-width: 300px;
          margin: 0 auto;
        }

        .member-image img {
          width: 100px;
          height: 100px;
        }

        .team-member {
          padding: 24px 20px;
        }
      }

      /* Mobile Styles - 412px */
      @media (max-width: 768px) {
        .container {
          padding: 48px 24px;
        }

        .team-header {
          margin-bottom: 48px;
        }

        .team-title {
          font-size: 32px;
        }

        .team-description {
          font-size: 16px;
        }

        .team-grid {
          grid-template-columns: 1fr;
          grid-template-rows: repeat(6, auto);
          gap: 20px;
        }

        .team-member:nth-child(1),
        .team-member:nth-child(2),
        .team-member:nth-child(4),
        .team-member:nth-child(5),
        .team-member:nth-child(6) {
          grid-column: 1;
        }

        .team-member:nth-child(1) { grid-row: 1; }
        .team-member:nth-child(2) { grid-row: 2; }
        .see-all-members {
          grid-row: 3;
          grid-column: 1;
          padding: 20px;
        }
        .team-member:nth-child(4) { grid-row: 4; }
        .team-member:nth-child(5) { grid-row: 5; }
        .team-member:nth-child(6) { grid-row: 6; }

        .team-member {
          padding: 24px 20px;
        }

        .member-image img {
          width: 80px;
          height: 80px;
        }

        .member-name {
          font-size: 18px;
        }

        .member-role {
          font-size: 14px;
        }

        .author-info {
          margin-top: 48px;
        }
      }
    </style>
  </head>
  <body>
    <main class="container">
      <header class="team-header">
        <h2 class="team-subtitle">Our team</h2>
        <h1 class="team-title">Meet the brain</h1>
        <p class="team-description">
          We are proud to have them as part of our community and look forward to continuing to push the boundaries of what's possible in the world of digital art.
        </p>
      </header>

      <section class="team-grid">
        <div class="team-member featured">
          <div class="member-image">
            <img src="resources/person_1.png" alt="Liam Novak" srcset="resources/<EMAIL> 2x">
          </div>
          <div class="member-info">
            <h3 class="member-name">Liam Novak</h3>
            <p class="member-role">Software Engineer</p>
          </div>
        </div>

        <div class="team-member featured">
          <div class="member-image">
            <img src="resources/person_2.png" alt="Sophia Moretti" srcset="resources/<EMAIL> 2x">
          </div>
          <div class="member-info">
            <h3 class="member-name">Sophia Moretti</h3>
            <p class="member-role">Business Development Manager</p>
          </div>
        </div>

        <div class="see-all-members">
          <a href="#" class="see-all-link">
            <span>See all members</span>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M7 17L17 7M17 7H7M17 7V17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </a>
        </div>

        <div class="team-member">
          <div class="member-image">
            <img src="resources/person_3.png" alt="Ethan Rossi" srcset="resources/<EMAIL> 2x">
          </div>
          <div class="member-info">
            <h3 class="member-name">Ethan Rossi</h3>
            <p class="member-role">Business Development Manager</p>
          </div>
        </div>

        <div class="team-member">
          <div class="member-image">
            <img src="resources/person_4.png" alt="Isabella Ricci" srcset="resources/<EMAIL> 2x">
          </div>
          <div class="member-info">
            <h3 class="member-name">Isabella Ricci</h3>
            <p class="member-role">UX Designer</p>
          </div>
        </div>

        <div class="team-member">
          <div class="member-image">
            <img src="resources/person_5.png" alt="Noah Conti" srcset="resources/<EMAIL> 2x">
          </div>
          <div class="member-info">
            <h3 class="member-name">Noah Conti</h3>
            <p class="member-role">Content Creator</p>
          </div>
        </div>
      </section>
    </main>

    <div class="author-info">
      Coded by <a href="#">Your Name Here</a> | Challenge by
      <a href="https://www.devchallenges.io?ref=challenge" target="_blank"
        >devChallenges.io</a
      >.
    </div>
  </body>
</html>
